<?php $__env->startSection('title', 'แดชบอร์ด - SoloShop Admin'); ?>

<?php $__env->startSection('content'); ?>
<!-- Welcome Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">สวัสดี, <?php echo e(Auth::user()->name); ?>! 👋</h1>
                <p class="text-muted mb-0">ยินดีต้อนรับสู่ระบบจัดการ SoloShop</p>
            </div>
            <div class="text-end">
                <small class="text-muted">วันที่: <?php echo e(now()->format('d/m/Y H:i')); ?></small>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-6 col-lg-3">
        <div class="card stats-card">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">บริการทั้งหมด</h6>
                        <h2 class="mb-0"><?php echo e($stats['services_count']); ?></h2>
                    </div>
                    <i class="fas fa-cogs fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">แพคเกจทั้งหมด</h6>
                        <h2 class="mb-0"><?php echo e($stats['packages_count']); ?></h2>
                    </div>
                    <i class="fas fa-box fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">กิจกรรมทั้งหมด</h6>
                        <h2 class="mb-0"><?php echo e($stats['activities_count']); ?></h2>
                    </div>
                    <i class="fas fa-images fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">ข้อความติดต่อ</h6>
                        <h2 class="mb-0"><?php echo e($stats['contacts_count']); ?></h2>
                        <?php if($stats['unread_contacts'] > 0): ?>
                        <small><?php echo e($stats['unread_contacts']); ?> ข้อความใหม่</small>
                        <?php endif; ?>
                    </div>
                    <i class="fas fa-envelope fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions & Analytics -->
<div class="row g-4 mb-5">
    <!-- Quick Actions -->
    <div class="col-lg-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2 text-warning"></i>การดำเนินการด่วน
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="<?php echo e(route('admin.services.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>เพิ่มบริการใหม่
                    </a>
                    <a href="<?php echo e(route('admin.packages.create')); ?>" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>เพิ่มแพคเกจใหม่
                    </a>
                    <a href="<?php echo e(route('admin.activities.create')); ?>" class="btn btn-warning">
                        <i class="fas fa-plus me-2"></i>เพิ่มกิจกรรมใหม่
                    </a>
                    <a href="<?php echo e(route('admin.settings')); ?>" class="btn btn-info">
                        <i class="fas fa-cog me-2"></i>ตั้งค่าเว็บไซต์
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart Analytics -->
    <div class="col-lg-8">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2 text-primary"></i>ภาพรวมข้อมูล
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statsChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities & Contacts -->
<div class="row g-4 mb-5">
    <!-- Recent Contacts -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-envelope me-2 text-info"></i>ข้อความติดต่อล่าสุด
                </h5>
                <a href="<?php echo e(route('admin.contacts')); ?>" class="btn btn-sm btn-outline-primary">ดูทั้งหมด</a>
            </div>
            <div class="card-body">
                <?php if($recent_contacts->count() > 0): ?>
                <div class="list-group list-group-flush">
                    <?php $__currentLoopData = $recent_contacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="list-group-item px-0 border-0 <?php echo e(!$contact->is_read ? 'bg-primary bg-opacity-10' : ''); ?>">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-1">
                                    <h6 class="mb-0 me-2"><?php echo e($contact->name); ?></h6>
                                    <?php if(!$contact->is_read): ?>
                                    <span class="badge bg-warning text-dark">ใหม่</span>
                                    <?php endif; ?>
                                </div>
                                <p class="mb-1 text-muted small"><?php echo e(Str::limit($contact->subject, 50)); ?></p>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i><?php echo e($contact->created_at->diffForHumans()); ?>

                                </small>
                            </div>
                            <a href="<?php echo e(route('admin.contacts.show', $contact->id)); ?>" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <?php else: ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3 opacity-50"></i>
                    <p class="mb-0">ยังไม่มีข้อความติดต่อ</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server me-2 text-success"></i>สถานะระบบ
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="text-center p-3 bg-success bg-opacity-10 rounded">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h6 class="mb-1">ระบบออนไลน์</h6>
                            <small class="text-muted">ทำงานปกติ</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 bg-info bg-opacity-10 rounded">
                            <i class="fas fa-database fa-2x text-info mb-2"></i>
                            <h6 class="mb-1">ฐานข้อมูล</h6>
                            <small class="text-muted">เชื่อมต่อแล้ว</small>
                        </div>
                    </div>
                    <div class="col-12">
                        <hr class="my-3">
                        <div class="row text-center">
                            <div class="col-4">
                                <small class="text-muted d-block">Laravel</small>
                                <strong><?php echo e(app()->version()); ?></strong>
                            </div>
                            <div class="col-4">
                                <small class="text-muted d-block">PHP</small>
                                <strong><?php echo e(PHP_VERSION); ?></strong>
                            </div>
                            <div class="col-4">
                                <small class="text-muted d-block">ผู้ใช้</small>
                                <strong><?php echo e(Auth::user()->name); ?></strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
// กราฟง่าย ๆ
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('statsChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['บริการ', 'แพคเกจ', 'กิจกรรม', 'ข้อความ'],
                datasets: [{
                    data: [
                        <?php echo e($stats['services_count']); ?>,
                        <?php echo e($stats['packages_count']); ?>,
                        <?php echo e($stats['activities_count']); ?>,
                        <?php echo e($stats['contacts_count']); ?>

                    ],
                    backgroundColor: [
                        '#4fc3f7',
                        '#28a745',
                        '#ffc107',
                        '#17a2b8'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>