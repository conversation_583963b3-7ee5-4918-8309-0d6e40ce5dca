<?php $__env->startSection('title', 'ตั้งค่าเว็บไซต์ - ระบบจัดการ'); ?>

<?php $__env->startSection('breadcrumb'); ?>
<li class="breadcrumb-item active">ตั้งค่าเว็บไซต์</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">ตั้งค่าเว็บไซต์</h1>
    <a href="<?php echo e(route('home')); ?>" class="btn btn-outline-primary" target="_blank">
        <i class="fas fa-external-link-alt me-2"></i>ดูเว็บไซต์
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <form action="<?php echo e(route('admin.settings.update')); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            
            <!-- General Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">ข้อมูลทั่วไป</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="site_name" class="form-label">ชื่อเว็บไซต์ <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php $__errorArgs = ['site_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="site_name" name="site_name" value="<?php echo e(old('site_name', $settings['site_name'])); ?>" required>
                        <?php $__errorArgs = ['site_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="site_description" class="form-label">คำอธิบายเว็บไซต์ <span class="text-danger">*</span></label>
                        <textarea class="form-control <?php $__errorArgs = ['site_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  id="site_description" name="site_description" rows="3" required><?php echo e(old('site_description', $settings['site_description'])); ?></textarea>
                        <?php $__errorArgs = ['site_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <div class="form-text">คำอธิบายนี้จะแสดงในหน้าหลักและ meta description</div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">ข้อมูลติดต่อ</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_phone" class="form-label">เบอร์โทรศัพท์ <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php $__errorArgs = ['contact_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="contact_phone" name="contact_phone" value="<?php echo e(old('contact_phone', $settings['contact_phone'])); ?>" required>
                                <?php $__errorArgs = ['contact_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_email" class="form-label">อีเมล <span class="text-danger">*</span></label>
                                <input type="email" class="form-control <?php $__errorArgs = ['contact_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="contact_email" name="contact_email" value="<?php echo e(old('contact_email', $settings['contact_email'])); ?>" required>
                                <?php $__errorArgs = ['contact_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="contact_address" class="form-label">ที่อยู่ <span class="text-danger">*</span></label>
                        <textarea class="form-control <?php $__errorArgs = ['contact_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  id="contact_address" name="contact_address" rows="3" required><?php echo e(old('contact_address', $settings['contact_address'])); ?></textarea>
                        <?php $__errorArgs = ['contact_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            </div>
            
            <!-- Social Media -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">โซเชียลมีเดีย</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="facebook_url" class="form-label">Facebook URL</label>
                                <input type="url" class="form-control <?php $__errorArgs = ['facebook_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="facebook_url" name="facebook_url" value="<?php echo e(old('facebook_url', $settings['facebook_url'])); ?>" 
                                       placeholder="https://facebook.com/yourpage">
                                <?php $__errorArgs = ['facebook_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="line_id" class="form-label">Line ID</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['line_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="line_id" name="line_id" value="<?php echo e(old('line_id', $settings['line_id'])); ?>" 
                                       placeholder="@yourlineid">
                                <?php $__errorArgs = ['line_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <div class="form-text">ใส่ Line ID โดยไม่ต้องใส่ @ ข้างหน้า</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>บันทึกการตั้งค่า
                </button>
                <button type="reset" class="btn btn-secondary">รีเซ็ต</button>
            </div>
        </form>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">ตัวอย่างการแสดงผล</h5>
            </div>
            <div class="card-body">
                <div class="preview-section">
                    <h6>ชื่อเว็บไซต์:</h6>
                    <p class="fw-bold" id="preview-site-name"><?php echo e($settings['site_name']); ?></p>
                    
                    <h6>คำอธิบาย:</h6>
                    <p class="text-muted" id="preview-site-description"><?php echo e($settings['site_description']); ?></p>
                    
                    <h6>ข้อมูลติดต่อ:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-phone text-primary me-2"></i><span id="preview-phone"><?php echo e($settings['contact_phone']); ?></span></li>
                        <li><i class="fas fa-envelope text-primary me-2"></i><span id="preview-email"><?php echo e($settings['contact_email']); ?></span></li>
                        <li><i class="fas fa-map-marker-alt text-primary me-2"></i><span id="preview-address"><?php echo e($settings['contact_address']); ?></span></li>
                    </ul>
                    
                    <h6>โซเชียลมีเดีย:</h6>
                    <div class="d-flex gap-2">
                        <?php if($settings['facebook_url']): ?>
                        <a href="<?php echo e($settings['facebook_url']); ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <?php endif; ?>
                        <?php if($settings['line_id']): ?>
                        <a href="https://line.me/ti/p/<?php echo e($settings['line_id']); ?>" class="btn btn-sm btn-outline-success" target="_blank">
                            <i class="fab fa-line"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">คำแนะนำ</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        ใช้ชื่อเว็บไซต์ที่จดจำง่ายและสื่อถึงธุรกิจ
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        เขียนคำอธิบายที่กระชับและน่าสนใจ
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        ระบุข้อมูลติดต่อให้ครบถ้วนและถูกต้อง
                    </li>
                    <li>
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        เพิ่มโซเชียลมีเดียเพื่อเพิ่มช่องทางติดต่อ
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
// Live preview
document.addEventListener('DOMContentLoaded', function() {
    const inputs = {
        'site_name': 'preview-site-name',
        'site_description': 'preview-site-description',
        'contact_phone': 'preview-phone',
        'contact_email': 'preview-email',
        'contact_address': 'preview-address'
    };
    
    Object.keys(inputs).forEach(inputId => {
        const input = document.getElementById(inputId);
        const preview = document.getElementById(inputs[inputId]);
        
        if (input && preview) {
            input.addEventListener('input', function() {
                preview.textContent = this.value || 'ไม่ได้ระบุ';
            });
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/settings.blade.php ENDPATH**/ ?>