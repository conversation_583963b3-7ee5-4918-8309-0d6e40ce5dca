<!DOCTYPE html>
<html lang="th" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $__env->yieldContent('title', 'SoloShop Admin'); ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom Admin CSS -->
    <link href="<?php echo e(asset('css/admin-custom.css')); ?>" rel="stylesheet">
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <style>
        /* ธีมสีง่าย ๆ */
        body {
            font-family: 'Kanit', sans-serif;
            transition: all 0.3s ease;
        }

        /* ธีมมืด (ค่าเริ่มต้น) */
        body.dark-theme {
            background-color: #1a1a1a;
            color: #ffffff;
        }

        body.dark-theme .top-navbar {
            background-color: #2d2d2d !important;
            border-bottom: 1px solid #444;
        }

        body.dark-theme .card {
            background-color: #2d2d2d;
            border: 1px solid #444;
            color: #ffffff;
        }

        body.dark-theme .navbar-brand {
            color: #4fc3f7 !important;
        }

        body.dark-theme .nav-link {
            color: #cccccc !important;
        }

        body.dark-theme .nav-link:hover,
        body.dark-theme .nav-link.active {
            background-color: #4fc3f7 !important;
            color: #ffffff !important;
        }

        body.dark-theme .dropdown-menu {
            background-color: #2d2d2d;
            border: 1px solid #444;
        }

        body.dark-theme .dropdown-item {
            color: #cccccc;
        }

        body.dark-theme .dropdown-item:hover {
            background-color: #4fc3f7;
            color: #ffffff;
        }

        /* ธีมสว่าง */
        body.light-theme {
            background-color: #f8f9fa;
            color: #333333;
        }

        body.light-theme .top-navbar {
            background-color: #ffffff !important;
            border-bottom: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        body.light-theme .card {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            color: #333333;
        }

        body.light-theme .navbar-brand {
            color: #007bff !important;
        }

        body.light-theme .nav-link {
            color: #6c757d !important;
        }

        body.light-theme .nav-link:hover,
        body.light-theme .nav-link.active {
            background-color: #007bff !important;
            color: #ffffff !important;
        }

        body.light-theme .dropdown-menu {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
        }

        body.light-theme .dropdown-item {
            color: #333333;
        }

        body.light-theme .dropdown-item:hover {
            background-color: #007bff;
            color: #ffffff;
        }

        /* สไตล์ทั่วไป */
        .top-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 70px;
            padding: 0.5rem 1rem;
        }

        .main-wrapper {
            margin-top: 70px;
            padding: 2rem;
            min-height: calc(100vh - 70px);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .nav-pills .nav-link {
            border-radius: 8px;
            padding: 8px 16px;
            margin: 0 4px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .card {
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 8px 16px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .stats-card {
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            color: white;
            border: none;
        }

        .alert {
            border-radius: 8px;
            border: none;
        }

        .theme-toggle {
            background: transparent;
            border: 2px solid currentColor;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            transform: scale(1.1);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-wrapper {
                padding: 1rem;
            }

            .nav-pills {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        /* Animation */
        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>

    <?php echo $__env->yieldContent('styles'); ?>
</head>
<body class="dark-theme">
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg top-navbar">
        <div class="container-fluid">
            <!-- Brand -->
            <a class="navbar-brand" href="<?php echo e(route('admin.dashboard')); ?>">
                <i class="fas fa-store me-2"></i>SoloShop Admin
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav nav-pills me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('admin.dashboard')); ?>">
                            <i class="fas fa-home me-2"></i>หน้าหลัก
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('admin.services*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.services')); ?>">
                            <i class="fas fa-cogs me-2"></i>บริการ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('admin.packages*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.packages')); ?>">
                            <i class="fas fa-box me-2"></i>แพคเกจ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('admin.activities*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.activities')); ?>">
                            <i class="fas fa-images me-2"></i>กิจกรรม
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('admin.contacts*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.contacts')); ?>">
                            <i class="fas fa-envelope me-2"></i>ข้อความ
                            <?php if(isset($unread_contacts) && $unread_contacts > 0): ?>
                                <span class="badge bg-danger ms-1"><?php echo e($unread_contacts); ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('admin.settings*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.settings')); ?>">
                            <i class="fas fa-cog me-2"></i>ตั้งค่า
                        </a>
                    </li>
                </ul>

                <!-- Right Side -->
                <div class="d-flex align-items-center gap-3">
                    <!-- Theme Toggle -->
                    <div class="theme-toggle" onclick="toggleTheme()" title="เปลี่ยนธีม">
                        <i class="fas fa-sun" id="theme-icon"></i>
                    </div>

                    <!-- User Dropdown -->
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?php echo e(Auth::user()->name); ?>

                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="<?php echo e(route('admin.services.create')); ?>">
                                <i class="fas fa-plus me-2"></i>เพิ่มบริการ
                            </a></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('admin.packages.create')); ?>">
                                <i class="fas fa-plus me-2"></i>เพิ่มแพคเกจ
                            </a></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('admin.activities.create')); ?>">
                                <i class="fas fa-plus me-2"></i>เพิ่มกิจกรรม
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('home')); ?>" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>ดูเว็บไซต์
                            </a></li>
                            <li>
                                <form action="<?php echo e(route('admin.logout')); ?>" method="POST" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content Wrapper -->
    <div class="main-wrapper">
        <!-- Alerts -->
        <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show fade-in-up" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show fade-in-up" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if($errors->any()): ?>
        <div class="alert alert-danger alert-dismissible fade show fade-in-up" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <ul class="mb-0">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Page Content -->
        <div class="fade-in-up">
            <?php echo $__env->yieldContent('content'); ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom Admin JS -->
    <script src="<?php echo e(asset('js/admin-custom.js')); ?>"></script>

    <!-- Simple JS -->
    <script>
        // ฟังก์ชันเปลี่ยนธีม
        function toggleTheme() {
            const body = document.body;
            const icon = document.getElementById('theme-icon');

            if (body.classList.contains('dark-theme')) {
                // เปลี่ยนเป็นธีมสว่าง
                body.classList.remove('dark-theme');
                body.classList.add('light-theme');
                icon.className = 'fas fa-moon';
                localStorage.setItem('admin-theme', 'light');
                console.log('เปลี่ยนเป็นธีมสว่าง');
            } else {
                // เปลี่ยนเป็นธีมมืด
                body.classList.remove('light-theme');
                body.classList.add('dark-theme');
                icon.className = 'fas fa-sun';
                localStorage.setItem('admin-theme', 'dark');
                console.log('เปลี่ยนเป็นธีมมืด');
            }
        }

        // โหลดธีมที่บันทึกไว้
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('admin-theme') || 'dark';
            const body = document.body;
            const icon = document.getElementById('theme-icon');

            console.log('ธีมที่บันทึกไว้:', savedTheme);

            // ลบ class เดิมก่อน
            body.classList.remove('dark-theme', 'light-theme');

            if (savedTheme === 'light') {
                body.classList.add('light-theme');
                icon.className = 'fas fa-moon';
            } else {
                body.classList.add('dark-theme');
                icon.className = 'fas fa-sun';
            }
        });

        // ซ่อน alert อัตโนมัติ
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.classList.contains('show')) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            });
        }, 5000);

        // เพิ่ม loading state ให้ปุ่ม
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn && !submitBtn.disabled) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังดำเนินการ...';
                    submitBtn.disabled = true;

                    // คืนค่าเดิมหลัง 10 วินาที (กรณีมีปัญหา)
                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 10000);
                }
            });
        });
    </script>

    <?php echo $__env->yieldContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/layouts/admin.blade.php ENDPATH**/ ?>