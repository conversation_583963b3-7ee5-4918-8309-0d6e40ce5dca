<?php $__env->startSection('title', 'กิจกรรม - ' . ($settings['site_name'] ?? 'บริษัทของเรา')); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<section class="hero-section">
    <div class="container">
        <div class="text-center">
            <h1 class="display-4 fw-bold mb-4">กิจกรรมของเรา</h1>
            <p class="lead">ติดตามกิจกรรมและผลงานที่เราได้ทำร่วมกับลูกค้า</p>
        </div>
    </div>
</section>

<!-- Activities Section -->
<section class="py-5">
    <div class="container">
        <?php if($activities->count() > 0): ?>
        <div class="row g-4">
            <?php $__currentLoopData = $activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-6 col-lg-4">
                <div class="card service-card h-100">
                    <img src="<?php echo e(asset('storage/' . $activity->image)); ?>" class="card-img-top" alt="<?php echo e($activity->title); ?>" style="height: 250px; object-fit: cover;">
                    
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title"><?php echo e($activity->title); ?></h5>
                        <p class="card-text flex-grow-1"><?php echo e($activity->description); ?></p>
                        
                        <div class="activity-meta mb-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-calendar text-primary me-2"></i>
                                <small class="text-muted"><?php echo e($activity->activity_date->format('d/m/Y')); ?></small>
                            </div>
                            <?php if($activity->location): ?>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                <small class="text-muted"><?php echo e($activity->location); ?></small>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php if($activity->details): ?>
                        <div class="mt-auto">
                            <button class="btn btn-outline-primary w-100" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#details-<?php echo e($activity->id); ?>" aria-expanded="false">
                                ดูรายละเอียด
                            </button>
                            <div class="collapse mt-3" id="details-<?php echo e($activity->id); ?>">
                                <div class="card card-body bg-light">
                                    <p class="small mb-0"><?php echo e($activity->details); ?></p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-images fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">ยังไม่มีกิจกรรม</h3>
            <p class="text-muted">กรุณาติดตามกิจกรรมของเราในอนาคต</p>
            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">ติดต่อเรา</a>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Gallery Section -->
<?php if($activities->count() > 0): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">แกลเลอรี่กิจกรรม</h2>
            <p class="text-muted">ภาพบรรยากาศจากกิจกรรมต่างๆ ที่ผ่านมา</p>
        </div>
        
        <div class="row g-3">
            <?php $__currentLoopData = $activities->take(8); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-3 col-6">
                <div class="gallery-item">
                    <img src="<?php echo e(asset('storage/' . $activity->image)); ?>" 
                         class="img-fluid rounded shadow-sm" 
                         alt="<?php echo e($activity->title); ?>"
                         style="height: 200px; width: 100%; object-fit: cover; cursor: pointer;"
                         data-bs-toggle="modal" 
                         data-bs-target="#imageModal"
                         data-image="<?php echo e(asset('storage/' . $activity->image)); ?>"
                         data-title="<?php echo e($activity->title); ?>"
                         data-description="<?php echo e($activity->description); ?>"
                         data-date="<?php echo e($activity->activity_date->format('d/m/Y')); ?>"
                         data-location="<?php echo e($activity->location); ?>">
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid rounded mb-3" alt="">
                <p id="modalDescription" class="text-muted"></p>
                <div class="d-flex justify-content-center gap-4">
                    <div id="modalDate"></div>
                    <div id="modalLocation"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Contact CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">สนใจร่วมกิจกรรมกับเรา?</h2>
                <p class="lead mb-4">ติดต่อเราเพื่อสอบถามเกี่ยวกับการร่วมงานและกิจกรรมต่างๆ</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg">ติดต่อเรา</a>
                    <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const imageModal = document.getElementById('imageModal');
    
    if (imageModal) {
        imageModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const image = button.getAttribute('data-image');
            const title = button.getAttribute('data-title');
            const description = button.getAttribute('data-description');
            const date = button.getAttribute('data-date');
            const location = button.getAttribute('data-location');
            
            document.getElementById('modalImage').src = image;
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalDescription').textContent = description;
            document.getElementById('modalDate').innerHTML = '<i class="fas fa-calendar text-primary me-2"></i>' + date;
            
            if (location) {
                document.getElementById('modalLocation').innerHTML = '<i class="fas fa-map-marker-alt text-primary me-2"></i>' + location;
            } else {
                document.getElementById('modalLocation').innerHTML = '';
            }
        });
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/frontend/activities.blade.php ENDPATH**/ ?>