@extends('layouts.admin')

@section('title', 'จัดการกิจกรรม - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item active">จัดการกิจกรรม</li>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">จัดการกิจกรรม</h1>
    <a href="{{ route('admin.activities.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>เพิ่มกิจกรรมใหม่
    </a>
</div>

<div class="card">
    <div class="card-body">
        @if($activities->count() > 0)
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>รูปภาพ</th>
                        <th>ชื่อกิจกรรม</th>
                        <th>คำอธิบาย</th>
                        <th>วันที่</th>
                        <th>สถานที่</th>
                        <th>สถานะ</th>
                        <th>ลำดับ</th>
                        <th>การจัดการ</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($activities as $activity)
                    <tr>
                        <td>
                            <img src="{{ asset('storage/' . $activity->image) }}" alt="{{ $activity->title }}" 
                                 class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                        </td>
                        <td>
                            <strong>{{ $activity->title }}</strong>
                        </td>
                        <td>
                            {{ Str::limit($activity->description, 50) }}
                        </td>
                        <td>
                            <span class="badge bg-info">{{ $activity->activity_date->format('d/m/Y') }}</span>
                        </td>
                        <td>
                            @if($activity->location)
                            {{ Str::limit($activity->location, 30) }}
                            @else
                            <span class="text-muted">ไม่ระบุ</span>
                            @endif
                        </td>
                        <td>
                            @if($activity->is_active)
                            <span class="badge bg-success">เปิดใช้งาน</span>
                            @else
                            <span class="badge bg-secondary">ปิดใช้งาน</span>
                            @endif
                        </td>
                        <td>
                            <span class="badge bg-info">{{ $activity->sort_order }}</span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('admin.activities.edit', $activity->id) }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.activities.delete', $activity->id) }}" 
                                      method="POST" class="d-inline"
                                      onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบกิจกรรมนี้?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
        <div class="text-center py-5">
            <i class="fas fa-images fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">ยังไม่มีกิจกรรม</h4>
            <p class="text-muted">เริ่มต้นด้วยการเพิ่มกิจกรรมแรกของคุณ</p>
            <a href="{{ route('admin.activities.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>เพิ่มกิจกรรมใหม่
            </a>
        </div>
        @endif
    </div>
</div>
@endsection
