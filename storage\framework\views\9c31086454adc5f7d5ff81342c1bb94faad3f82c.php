<?php $__env->startSection('title', 'จัดการกิจกรรม - ระบบจัดการ'); ?>

<?php $__env->startSection('breadcrumb'); ?>
<li class="breadcrumb-item active">จัดการกิจกรรม</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">จัดการกิจกรรม</h1>
    <a href="<?php echo e(route('admin.activities.create')); ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>เพิ่มกิจกรรมใหม่
    </a>
</div>

<div class="card">
    <div class="card-body">
        <?php if($activities->count() > 0): ?>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>รูปภาพ</th>
                        <th>ชื่อกิจกรรม</th>
                        <th>คำอธิบาย</th>
                        <th>วันที่</th>
                        <th>สถานที่</th>
                        <th>สถานะ</th>
                        <th>ลำดับ</th>
                        <th>การจัดการ</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>
                            <img src="<?php echo e(asset('storage/' . $activity->image)); ?>" alt="<?php echo e($activity->title); ?>" 
                                 class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                        </td>
                        <td>
                            <strong><?php echo e($activity->title); ?></strong>
                        </td>
                        <td>
                            <?php echo e(Str::limit($activity->description, 50)); ?>

                        </td>
                        <td>
                            <span class="badge bg-info"><?php echo e($activity->activity_date->format('d/m/Y')); ?></span>
                        </td>
                        <td>
                            <?php if($activity->location): ?>
                            <?php echo e(Str::limit($activity->location, 30)); ?>

                            <?php else: ?>
                            <span class="text-muted">ไม่ระบุ</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($activity->is_active): ?>
                            <span class="badge bg-success">เปิดใช้งาน</span>
                            <?php else: ?>
                            <span class="badge bg-secondary">ปิดใช้งาน</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge bg-info"><?php echo e($activity->sort_order); ?></span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?php echo e(route('admin.activities.edit', $activity->id)); ?>" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="<?php echo e(route('admin.activities.delete', $activity->id)); ?>" 
                                      method="POST" class="d-inline"
                                      onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบกิจกรรมนี้?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-images fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">ยังไม่มีกิจกรรม</h4>
            <p class="text-muted">เริ่มต้นด้วยการเพิ่มกิจกรรมแรกของคุณ</p>
            <a href="<?php echo e(route('admin.activities.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>เพิ่มกิจกรรมใหม่
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/activities/index.blade.php ENDPATH**/ ?>